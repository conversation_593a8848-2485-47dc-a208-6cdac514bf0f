import axios from 'axios';

export const itemNameAPI = async (branchId, binId, itemCategoryId, bearerToken) => {
  const stockUrl = `https://retailuat.abisibg.com/api/v1/fetchstock?BranchID=${branchId}&BinId=${binId}&ItemStatusID=OK&ItemCategoryId=${itemCategoryId}&Channel=ALL&ItemID=ALL&BatchNumber=ALL`;
  const businessDateUrl = `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`;

  try {
    // 1. Fetch current business date
    const businessDateResponse = await axios.get(businessDateUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    const businessDateRaw = businessDateResponse.data?.[0]?.BusinessDateCode;
    if (!businessDateRaw) {
      throw new Error("Could not retrieve business date.");
    }

    // Format business date to YYYYMMDD
    const formattedBusinessDate = businessDateRaw.slice(0, 10).replace(/-/g, '');

    // 2. Fetch stock items
    const stockResponse = await axios.get(stockUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (stockResponse.status !== 200) {
      return {
        success: false,
        message: 'No items found.',
      };
    }

    // 3. Filter by stock quantities
    const validStockItems = stockResponse.data.filter(item =>
      item.StockQty > 0 && item.StockAltQty >= 0
    );

    // 4. Fetch channel rate for each item and filter
    const rateCheckPromises = validStockItems.map(async (item) => {
      const rateUrl = `https://retailuat.abisaio.com:9001/api/ChannelRate/${branchId}/${formattedBusinessDate}/VAN/${item.ItemID}/ALL`;

      try {
        const rateResponse = await axios.get(rateUrl, {
          headers: {
            Authorization: `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
          },
        });

        const rate = parseFloat(rateResponse.data);
        return rate > 0 ? item : null;
      } catch (rateError) {
        console.warn(`Failed to fetch rate for item ${item.ItemID}:`, rateError.message);
        return null;
      }
    });

    const filteredItemsWithPrice = (await Promise.all(rateCheckPromises)).filter(Boolean);

    return {
      success: true,
      itemNames: filteredItemsWithPrice,
    };

  } catch (error) {
    console.error('Error fetching items:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
