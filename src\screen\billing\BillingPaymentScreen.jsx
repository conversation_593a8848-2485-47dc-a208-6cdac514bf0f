import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { getPaymentMethodsAPI } from '../../apiHandling/PaymentMethodAPI';
import { saveBillingData } from '../../apiHandling/SaveBillingAPI'; // <--- Import your API function
import { loginBranchID, bearerToken, defaultBinId, loginBranchName, loginUserID } from '../../globals';
import { tokens } from 'react-native-paper/lib/typescript/styles/themes/v3/tokens';

const BillingPaymentScreen = () => {
    const navigation = useNavigation();

    // Payment methods state
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
    const [utrNo, setUtrNo] = useState('');
    const [amountReceived, setAmountReceived] = useState('');

    const route = useRoute();
    const {
        customerId,
        customerName,
        customerMobile,
        selectedSaleType,
        selectedBusinessChannel,
        itemTableDetails,
        summary,
        total,
    } = route.params;

    useEffect(() => {
        const fetchPaymentMethods = async () => {
            const result = await getPaymentMethodsAPI(loginBranchID, bearerToken);
            if (Array.isArray(result)) {
                setPaymentMethods(result);
            } else {
                setPaymentMethods([]);
            }
        };
        fetchPaymentMethods();
    }, []);

    const handleSave = async () => {
        if (!selectedPaymentMethod) {
            alert('Please select a payment method.');
            return;
        }

        // Map itemTableDetails to API expected structure
        const mappedItems = (itemTableDetails || []).map(item => ({
            ItemID: item.itemId,
            ItemName: item.itemName,
            ItemCategoryID: item.itemCategoryId,
            BatchNo: item.batchNumber,
            ItemPrice: item.price,
            ItemDayRate: item.itemDayRate,
            ItemWeight: item.weight,
            ItemQuantity: item.quantity,
            StockQty: item.stockQty,
            ItemTotalPrice: item.totalPrice,
            TaxPercent: item.taxPercent,
            TaxAmount: item.taxAmount,
            Discount: item.discount,
            ItemStockGroupID: item.stockGroupId,
            ItemTaxCategoryID: item.taxCategoryId,
            ItemSellByWeight: item.sellByWeight,
            ItemAltQtyEnabled: item.altQtyEnabled,
        }));

        const payload = {
            token: bearerToken,
            loginBranchID,
            loginUserID,
            CustomerID: customerId,
            CustomerName: customerName,
            CustomerMobile: customerMobile,
            saleType: selectedSaleType,
            businessChannel: selectedBusinessChannel,
            items: mappedItems, // <-- use mapped items
            billSummary: summary,
            totalAmount: total,
            branchId: loginBranchID,
            binId: defaultBinId,
            branchName: loginBranchName,
            utrNo: utrNo || '',
            amountReceived: parseFloat(amountReceived) || 0,
            paymentMethodId: selectedPaymentMethod.PaymentMethodId,
            paymentGatewayId: selectedPaymentMethod.PaymentGatewayId,
            paymentGatewayName: selectedPaymentMethod.PaymentGatewayName,

        };

        const result = await saveBillingData(payload);

        if (result?.result === 1) {
            alert('Billing saved successfully');
            navigation.navigate('Billing'); // Or redirect wherever appropriate
        } else {
            alert(result?.description || 'Failed to save billing');
        }
    };

    return (
        <View style={styles.container}>
            {/* App Bar */}
            <View style={styles.appBar}>
                <Text style={styles.appBarTitle}>Payment Screen</Text>
                <TouchableOpacity
                    style={styles.appBarBackButton}
                    onPress={() => navigation.navigate('Billing')}
                >
                    <Text style={styles.appBarBackButtonText}>Back</Text>
                </TouchableOpacity>
            </View>

            <ScrollView contentContainerStyle={styles.bodyContent} keyboardShouldPersistTaps="handled">
                {/* Row 1: UTR No. & Amount */}
                <View style={styles.row}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>UTR No.</Text>
                        <TextInput
                            style={styles.input}
                            placeholder="UTR No."
                            keyboardType="numeric"
                            value={utrNo}
                            onChangeText={setUtrNo}
                        />
                    </View>
                    <View style={{ width: 16 }} />
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Amount</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Amount"
                                keyboardType="numeric"
                                value={total.toString()}
                                editable={false}
                            />
                        </View>
                    </View>
                </View>

                {/* Row 2: Bill Amount, Total Amount Received, Change */}
                <View style={styles.row}>
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Bill Amount</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Bill Amount"
                                keyboardType="numeric"
                                value={total.toString()}
                                editable={false}
                            />
                        </View>
                    </View>
                    <View style={{ width: 16 }} />
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>Total Amount Received</Text>
                        <View style={styles.inputWithIcon}>
                            <Text style={styles.rupeeIcon}>₹</Text>
                            <TextInput
                                style={[styles.input, { flex: 1 }]}
                                placeholder="Total Amount Received"
                                keyboardType="numeric"
                                value={amountReceived}
                                onChangeText={setAmountReceived}
                            />
                        </View>
                    </View>
                </View>
                <View style={styles.row}>
                    <View style={[styles.inputContainer, { flex: 1 }]}>
                        <Text style={styles.label}>Change:</Text>
                        <Text style={styles.changeText}>
                            ₹{Math.max((parseFloat(amountReceived) || 0) - total, 0).toFixed(2)}
                        </Text>
                    </View>
                </View>

                {/* Payment Methods Buttons */}
                <View style={styles.paymentMethodsRow}>
                    {paymentMethods.map((method) => (
                        <TouchableOpacity
                            key={method.PaymentMethodId}
                            style={[
                                styles.paymentButton,
                                selectedPaymentMethod?.PaymentMethodId === method.PaymentMethodId
                                    ? styles.paymentButtonSelected
                                    : null,
                            ]}
                            onPress={() => setSelectedPaymentMethod(method)}
                        >
                            <Text
                                style={[
                                    styles.paymentButtonText,
                                    selectedPaymentMethod?.PaymentMethodId === method.PaymentMethodId
                                        ? styles.paymentButtonTextSelected
                                        : null,
                                ]}
                            >
                                {method.PaymentGatewayName}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>

                {/* Save Button */}
                <View style={styles.saveRow}>
                    <TouchableOpacity
                        style={styles.saveButton}
                        onPress={handleSave}
                    >
                        <Text style={styles.saveButtonText}>Save</Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    appBar: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'rgb(2,9,106)',
        paddingHorizontal: 20,
        paddingVertical: 18,
        borderBottomLeftRadius: 16,
        borderBottomRightRadius: 16,
        elevation: 4,
    },
    appBarTitle: {
        color: '#fff',
        fontSize: 22,
        fontWeight: 'bold',
        letterSpacing: 1,
    },
    appBarBackButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(255,255,255,0.08)',
    },
    appBarBackButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    bodyContent: {
        padding: 24,
        paddingBottom: 40,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 18,
    },
    inputContainer: {
        flex: 1,
    },
    label: {
        fontSize: 15,
        fontWeight: '600',
        marginBottom: 6,
        color: '#222',
    },
    input: {
        borderWidth: 1,
        borderColor: '#bfc4d1',
        borderRadius: 10,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#f8f9fa',
        color: '#222',
    },
    inputWithIcon: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rupeeIcon: {
        fontSize: 18,
        color: '#888',
        marginRight: 6,
    },
    changeText: {
        fontSize: 20,
        fontWeight: 'bold',
        color: 'green',
        marginTop: 8,
        marginLeft: 8,
    },
    paymentMethodsRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginVertical: 24,
        gap: 16,
        flexWrap: 'wrap',
    },
    paymentButton: {
        backgroundColor: '#e0e3ee',
        paddingVertical: 14,
        paddingHorizontal: 32,
        borderRadius: 10,
        marginHorizontal: 6,
        marginBottom: 8,
        minWidth: 110,
        alignItems: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    paymentButtonSelected: {
        backgroundColor: '#02096A',
        borderColor: '#02096A',
    },
    paymentButtonText: {
        color: '#02096A',
        fontWeight: 'bold',
        fontSize: 16,
    },
    paymentButtonTextSelected: {
        color: '#fff',
    },
    saveRow: {
        flexDirection: 'row',
        marginTop: 24,
    },
    saveButton: {
        flex: 1,
        backgroundColor: 'rgb(2,9,106)',
        paddingVertical: 16,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 2,
    },
    saveButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 19,
        letterSpacing: 1,
    },
});

export default BillingPaymentScreen;
