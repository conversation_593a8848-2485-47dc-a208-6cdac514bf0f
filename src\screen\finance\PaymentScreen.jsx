import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, branchId, branchName } from '../../globals';

const PaymentScreen = () => {
  const [selectedScrollOption, setSelectedScrollOption] = useState('');
  const [selectedTransferType, setSelectedTransferType] = useState('');
  const [documentNo, setDocumentNo] = useState('');
  const [branchKey, setBranchKey] = useState('');
  const [expensesGroup, setExpensesGroup] = useState('');
  const [expensesHead, setExpensesHead] = useState('');
  const [tripId, setTripId] = useState('');
  const [budgetAmount, setBudgetAmount] = useState('');
  const [remark, setRemark] = useState('');
  const [paymentMode, setPaymentMode] = useState('');
  const [amount, setAmount] = useState('');
  const [totalAmount, setTotalAmount] = useState('0');
  
  const [tableData, setTableData] = useState([
    { id: 1, lineNumber: '001', bankAccountName: 'HDFC Bank', remark: 'Salary payment', amount: '25000', isChecked: false },
    { id: 2, lineNumber: '002', bankAccountName: 'SBI Bank', remark: 'Vendor payment', amount: '15000', isChecked: false }
  ]);

  const toggleCheckbox = (id) => {
    const newData = tableData.map(item => 
      item.id === id ? { ...item, isChecked: !item.isChecked } : item
    );
    setTableData(newData);
  };

  const handleAddItem = () => {
    if (remark && amount) {
      const newItem = {
        id: Date.now(),
        lineNumber: (tableData.length + 1).toString().padStart(3, '0'),
        bankAccountName: paymentMode || 'Not specified',
        remark: remark,
        amount: amount,
        isChecked: false
      };
      setTableData([...tableData, newItem]);
      
      // Calculate new total
      const newTotal = tableData.reduce((sum, item) => sum + Number(item.amount), 0) + Number(amount);
      setTotalAmount(newTotal.toString());
      
      // Clear fields
      setRemark('');
      setAmount('');
    }
  };

  const handleClear = () => {
    setRemark('');
    setAmount('');
    setPaymentMode('');
  };

  const handleDeleteSelected = () => {
    const newData = tableData.filter(item => !item.isChecked);
    setTableData(newData);
    
    // Recalculate total
    const newTotal = newData.reduce((sum, item) => sum + Number(item.amount), 0);
    setTotalAmount(newTotal.toString());
    
    // Renumber line numbers
    const renumberedData = newData.map((item, index) => ({
      ...item,
      lineNumber: (index + 1).toString().padStart(3, '0')
    }));
    setTableData(renumberedData);
  };

  return (
    <View style={styles.container}>
      <Navbar />
      
      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Payment</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              const isSelected = selectedScrollOption === option;
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02720F' : '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                    onPress={() => setSelectedScrollOption(option)}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: isSelected ? 'white' : 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.paymentContainer}>
          {/* Row 1: Document No., Cash on hand */}
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textField}
              placeholder="Document Nos."
              value={documentNo}
              onChangeText={setDocumentNo}
            />
            <TextInput
              style={styles.textField}
              placeholder="Cash on hand"
              editable={false}
            />
          </View>

          {/* Row 2: Expenses Group, Expenses Head */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={[
                  { label: 'Group 1', value: 'Group 1' },
                  { label: 'Group 2', value: 'Group 2' },
                  { label: 'Group 3', value: 'Group 3' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Expenses Group"
                value={expensesGroup}
                onChange={(item) => setExpensesGroup(item.value)}
                style={styles.dropdown}
              />
            </View>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={[
                  { label: 'Expense 1', value: 'Expense 1' },
                  { label: 'Expense 2', value: 'Expense 2' },
                  { label: 'Expense 3', value: 'Expense 3' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Expenses Head"
                value={expensesHead}
                onChange={(item) => setExpensesHead(item.value)}
                style={styles.dropdown}
              />
            </View>
          </View>

          {/* Row 3: Budget Amount, Remark */}
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textField}
              placeholder="Budget Amount"
              value={budgetAmount}
              onChangeText={setBudgetAmount}
              keyboardType="numeric"
            />
            <TextInput
              style={styles.textField}
              placeholder="Remark"
              value={remark}
              onChangeText={setRemark}
            />
          </View>

          {/* Row 4: Payment Mode, Amount */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Dropdown
                data={[
                  { label: 'Cash', value: 'Cash' },
                  { label: 'Credit Card', value: 'Credit Card' },
                  { label: 'Bank Transfer', value: 'Bank Transfer' },
                  { label: 'HDFC Bank', value: 'HDFC Bank' },
                  { label: 'SBI Bank', value: 'SBI Bank' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Payment Mode"
                value={paymentMode}
                onChange={(item) => setPaymentMode(item.value)}
                style={styles.dropdown}
              />
            </View>
            <TextInput
              style={styles.textField}
              placeholder="Amount"
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
            />
          </View>

          {/* Row 5: Add and Clear Buttons */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddItem}
            >
              <Text style={styles.buttonText}>Add</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClear}
            >
              <Text style={styles.buttonText}>Clear</Text>
            </TouchableOpacity>
          </View>

          {/* Row 6: Table */}
          <View style={styles.tableContainer}>
            <DataTable>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.checkboxColumn}></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Line No</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Bank Account Name</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Remark</Text></DataTable.Title>
                <DataTable.Title><Text style={styles.tableHeaderText}>Amount</Text></DataTable.Title>
              </DataTable.Header>

              {tableData.map((item) => (
                <DataTable.Row key={item.id}>
                  <DataTable.Cell style={styles.checkboxColumn}>
                    <TouchableOpacity onPress={() => toggleCheckbox(item.id)}>
                      <View style={[styles.checkbox, item.isChecked && styles.checkboxChecked]}>
                        {item.isChecked && <Text style={styles.checkmark}>✓</Text>}
                      </View>
                    </TouchableOpacity>
                  </DataTable.Cell>
                  <DataTable.Cell>{item.lineNumber}</DataTable.Cell>
                  <DataTable.Cell>{item.bankAccountName}</DataTable.Cell>
                  <DataTable.Cell>{item.remark}</DataTable.Cell>
                  <DataTable.Cell>{item.amount}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          </View>

          {/* Row 7: Delete Button and Total Amount */}
          <View style={styles.finalRow}>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDeleteSelected}
            >
              <Text style={styles.buttonText}>Delete Selected Row</Text>
            </TouchableOpacity>
            
            <TextInput
              style={styles.totalField}
              placeholder="Amount"
              value={totalAmount}
              editable={false}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  
  // Scroll Options Styles (keeping the same from original)
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  
  // Payment Screen Styles
  paymentContainer: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  textField: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    marginHorizontal: 4,
  },
  dropdownContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  dropdown: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 10,
  },
  addButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  clearButton: {
    flex: 1,
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  tableContainer: {
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: 'white',
  },
  tableHeader: {
    backgroundColor: '#02096A',
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  checkboxColumn: {
    maxWidth: 40,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#02096A',
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#02096A',
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
  },
  finalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  deleteButton: {
    height: 50,
    backgroundColor: 'red',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    flex: 2,
    marginRight: 10,
  },
  totalField: {
    flex: 1,
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    textAlign: 'right',
  }
});

export default PaymentScreen;