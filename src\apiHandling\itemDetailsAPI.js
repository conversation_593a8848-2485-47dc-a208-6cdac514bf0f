import axios from 'axios';

export const itemDetailsAPI = async (itemId, bearerToken) => {
  const baseUrl = 'https://retailuat.abisibg.com/api/v1/itemdetail';
  const url = `${baseUrl}?PartNumber=${itemId}`;

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && Array.isArray(response.data) && response.data.length > 0) {
      const itemDetail = response.data[0]; // ✅ Get the first item from the array
      return {
        success: true,
        itemDetails: itemDetail,
      };
    } else {
      return {
        success: false,
        message: 'No item details found',
      };
    }
  } catch (error) {
    console.error('Error fetching details', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
