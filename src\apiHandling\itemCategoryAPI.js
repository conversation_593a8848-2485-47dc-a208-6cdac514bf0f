import axios from 'axios';

export const itemCategoryAPI = async (branchId, bearerToken) => {
  const baseUrl = 'https://retailuat.abisibg.com//api/v1/itemcategorylist';
  const url = `${baseUrl}?branchId=${branchId}`;

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200) {
      return {
        success: true,
        categories: response.data,
      };
    } else {
      return {
        success: false,
        message: 'No category found.',
      };
    }
  } catch (error) {
    console.error('Error fetching categories:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
