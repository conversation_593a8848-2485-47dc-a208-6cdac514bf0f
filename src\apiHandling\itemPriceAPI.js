import axios from 'axios';

export const itemPriceAPI = async (branchId, itemId, bearerToken) => {
  const businessDateUrl = `https://retailuat.abisibg.com/api/v1/currentbusinessday?BranchId=${branchId}`;

  try {
    // Step 1: Get the current business date
    const dateResponse = await axios.get(businessDateUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    const rawDate = dateResponse.data?.[0]?.BusinessDateCode;
    if (!rawDate) {
      throw new Error('Business date not found');
    }

    // Step 2: Format business date to YYYYMMDD
    const formattedDate = new Date(rawDate)
      .toISOString()
      .split('T')[0]
      .replace(/-/g, '');

    // Step 3: Build the price API URL
    const priceUrl = `https://retailuat.abisaio.com:9001/api/ChannelRate/${branchId}/${formattedDate}/VAN/${itemId}/ALL`;

    // Step 4: Fetch item price
    const priceResponse = await axios.get(priceUrl, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    return priceResponse.data; // e.g., 33.25
  } catch (error) {
    console.error('Error fetching item price:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
